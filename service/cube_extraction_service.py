import numpy as np
from itertools import combinations
from collections import Counter

class CubeExtractionService:
    def __init__(self):
        pass
    
    def extract_cubes_from_views(self, front_view, side_view, top_view):
        """
        从三视图提取立方体坐标
        :param front_view: 主视图坐标列表 (XZ平面)
        :param side_view: 左视图坐标列表 (YZ平面)
        :param top_view: 俯视图坐标列表 (XY平面)
        :return: 立方体坐标列表和处理后的三视图坐标
        """
        # 第一步：对三视图坐标进行归一化处理
        normalized_front = self.normalize_points(front_view)
        normalized_side = self.normalize_points(side_view)
        normalized_top = self.normalize_points(top_view)

        # 第二步：重新映射画布坐标
        canvas_front = self.convert_to_canvas_coordinates(normalized_front)
        canvas_side = self.convert_to_canvas_coordinates(normalized_side)
        canvas_top = self.convert_to_canvas_coordinates(normalized_top, is_top_view=True)
        
        # 第三步：提取所有可能的x, y, z坐标值（使用画布坐标）
        x_vals = sorted(set(x for x, _ in canvas_front) | set(x for x, _ in canvas_top))
        y_vals = sorted(set(y for y, _ in canvas_side) | set(y for _, y in canvas_top))
        z_vals = sorted(set(z for _, z in canvas_front) | set(z for _, z in canvas_side))
        
        # 转为集合便于判断（使用画布坐标）
        front_set = set(canvas_front)
        side_set = set(canvas_side)
        top_set = set(canvas_top)

        # 第四步：找出所有满足三视图约束的立方体中心坐标
        cube_positions = []
        cube_id = 1
        
        for x in x_vals:
            for y in y_vals:
                for z in z_vals:
                    if (x, z) in front_set and (y, z) in side_set and (x, y) in top_set:
                        cube_positions.append({"id": cube_id, "position": [x, y, z]})
                        cube_id += 1
        
        # 构建返回结果
        result = {
            "cubeList": cube_positions,
            "threeViews": {
                "frontViewList": canvas_front,
                "sideViewList": canvas_side,
                "topViewList": canvas_top
            }
        }
        
        return result
    
    def cluster_coords(self, coords, threshold=20):
        """
        对一维坐标进行聚类
        """
        coords = sorted(coords)
        clusters = []
        current = [coords[0]]
        for c in coords[1:]:
            if abs(c - current[-1]) <= threshold:
                current.append(c)
            else:
                clusters.append(current)
                current = [c]
        clusters.append(current)
        return [int(round(np.mean(cluster))) for cluster in clusters]
    
    def normalize_points(self, points, threshold=20):
        """
        对二维坐标点进行归一化处理
        """
        if not points:
            return []
            
        xs = [p[0] for p in points]
        ys = [p[1] for p in points]
        x_centers = self.cluster_coords(xs, threshold)
        y_centers = self.cluster_coords(ys, threshold)
        
        def find_nearest(val, centers):
            return min(centers, key=lambda c: abs(val-c))
            
        normed = [(find_nearest(x, x_centers), find_nearest(y, y_centers)) for x, y in points]
        return normed
    
    def cluster_points(self, points, threshold=20):
        """
        对二维坐标点进行聚类
        """
        if not points:
            return []
            
        points = sorted(points)
        clusters = []
        current = [points[0]]
        
        for p in points[1:]:
            if np.linalg.norm(np.array(p) - np.array(current[-1])) <= threshold:
                current.append(p)
            else:
                clusters.append(current)
                current = [p]
                
        clusters.append(current)
        return [tuple(np.mean(cluster, axis=0)) for cluster in clusters]

    def calculate_standard_length(self, points):
        """
        计算标准单元长度
        """
        if not points or len(points) < 2:
            return 0

        # 统计所有横向和纵向距离，找标准边长
        x_vals = sorted(set([p[0] for p in points]))
        y_vals = sorted(set([p[1] for p in points]))
        x_dists = [abs(x2-x1) for x1, x2 in combinations(x_vals, 2) if abs(x2-x1) > 0]
        y_dists = [abs(y2-y1) for y1, y2 in combinations(y_vals, 2) if abs(y2-y1) > 0]
        all_dists = x_dists + y_dists

        if all_dists:
            standard_len = Counter(all_dists).most_common(1)[0][0]
        else:
            standard_len = 0

        return standard_len

    def find_canvas_origin(self, points, is_top_view=False):
        """
        找到画布坐标系的原点
        :param points: 坐标点列表
        :param is_top_view: 是否为俯视图
        :return: 原点坐标
        """
        if not points:
            return (0, 0)

        if is_top_view:
            # 俯视图：找到左上角原点（最小x, 最小y）
            min_x = min(c[0] for c in points)
            min_y = min(c[1] for c in points)
            return (min_x, min_y)
        else:
            # 正视图/左视图：找到左下角点（x最小且y最大）
            canvas_origin = min(points, key=lambda c: (c[0], -c[1]))
            return canvas_origin

    def convert_to_canvas_coordinates(self, points, is_top_view=False):
        """
        将归一化坐标转换为画布坐标
        :param points: 归一化后的坐标点列表
        :param is_top_view: 是否为俯视图（俯视图需要特殊的y轴处理）
        :return: 画布坐标点列表
        """
        if not points:
            return []

        # 计算标准单元长度
        standard_len = self.calculate_standard_length(points)
        if standard_len == 0:
            return points  # 如果无法计算标准长度，返回原坐标

        # 找到画布坐标系原点
        x0, y0 = self.find_canvas_origin(points, is_top_view)

        # 转换为画布坐标
        canvas_points = []
        for x, y in points:
            dx = x - x0

            if is_top_view:
                # 俯视图：y轴向下为正（参考3.py的逻辑）
                dy = y - y0  # y正下
            else:
                # 正视图、左视图：y轴向上为正（参考2.py的逻辑）
                dy = y0 - y  # y轴翻转

            canvas_x = dx / standard_len * 2 + 1
            canvas_y = dy / standard_len * 2 + 1

            canvas_points.append((round(canvas_x), round(canvas_y)))

        return canvas_points