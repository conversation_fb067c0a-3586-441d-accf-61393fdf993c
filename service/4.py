import numpy as np
from itertools import combinations
from collections import Counter


def cluster_coords(coords, threshold=20):
    coords = sorted(coords)
    clusters = []
    current = [coords[0]]
    for c in coords[1:]:
        if abs(c - current[-1]) <= threshold:
            current.append(c)
        else:
            clusters.append(current)
            current = [c]
    clusters.append(current)
    return [int(round(np.mean(cluster))) for cluster in clusters]


def normalize_points(points, threshold=20):
    xs = [p[0] for p in points]
    ys = [p[1] for p in points]
    x_centers = cluster_coords(xs, threshold)
    y_centers = cluster_coords(ys, threshold)

    def find_nearest(val, centers):
        return min(centers, key=lambda c: abs(val - c))

    normed = [(find_nearest(x, x_centers), find_nearest(y, y_centers)) for x, y in points]
    return normed


def cluster_points(points, threshold=20):
    if not points:
        return []
    points = sorted(points)
    clusters = []
    current = [points[0]]
    for p in points[1:]:
        if np.linalg.norm(np.array(p) - np.array(current[-1])) <= threshold:
            current.append(p)
        else:
            clusters.append(current)
            current = [p]
    clusters.append(current)
    return [tuple(np.mean(cluster, axis=0)) for cluster in clusters]


# 输入点
input_points = [
    (129, 899), (399, 906), (684, 913),
    (114, 624), (404, 631), (684, 638),
    (406, 348), (684, 363),
    (413, 88), (684, 88)
]

# 第一步：归一化
normed_points = normalize_points(input_points, threshold=20)
point_set = set(normed_points)

# 统计所有横向和纵向距离，找标准边长
x_vals = sorted(set([p[0] for p in normed_points]))
y_vals = sorted(set([p[1] for p in normed_points]))
x_dists = [abs(x2 - x1) for x1, x2 in combinations(x_vals, 2) if abs(x2 - x1) > 0]
y_dists = [abs(y2 - y1) for y1, y2 in combinations(y_vals, 2) if abs(y2 - y1) > 0]
all_dists = x_dists + y_dists
if all_dists:
    standard_len = Counter(all_dists).most_common(1)[0][0]
else:
    standard_len = 0

# 方案一：对角点+另外两个顶点都存在
center_candidates = set()
for a, b in combinations(normed_points, 2):
    dx = abs(a[0] - b[0])
    dy = abs(a[1] - b[1])
    if dx > 0 and dy > 0 and abs(dx - dy) < 20:
        if abs(dx - standard_len) < 20 and abs(dy - standard_len) < 20:
            c = (a[0], b[1])
            d = (b[0], a[1])
            if c in point_set and d in point_set:
                center = ((a[0] + b[0]) / 2, (a[1] + b[1]) / 2)
                center_candidates.add(center)

# 对所有中心点聚类去重
square_centers = cluster_points(list(center_candidates), threshold=10)

print("标准单元正方形边长:", standard_len)
print("正方形中心点坐标：")
for c in square_centers:
    print(f"({c[0]:.1f}, {c[1]:.1f})")
