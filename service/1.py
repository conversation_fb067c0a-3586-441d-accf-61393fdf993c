import numpy as np


def cluster_coords(coords, threshold=20):
    coords = sorted(coords)
    clusters = []
    current = [coords[0]]
    for c in coords[1:]:
        if abs(c - current[-1]) <= threshold:
            current.append(c)
        else:
            clusters.append(current)
            current = [c]
    clusters.append(current)
    # 用均值代表每组
    return [int(round(np.mean(cluster))) for cluster in clusters]


def normalize_points(points, threshold=20):
    xs = [p[0] for p in points]
    ys = [p[1] for p in points]
    x_centers = cluster_coords(xs, threshold)
    y_centers = cluster_coords(ys, threshold)

    def find_nearest(val, centers):
        return min(centers, key=lambda c: abs(val - c))

    normed = [(find_nearest(x, x_centers), find_nearest(y, y_centers)) for x, y in points]
    return normed, x_centers, y_centers


# 测试数据
input_points = [
    (129, 899), (399, 906), (684, 913),
    (114, 624), (404, 631), (684, 638),
    (406, 348), (684, 363),
    (413, 88), (684, 88)
]

normed_points, x_centers, y_centers = normalize_points(input_points, threshold=20)
print("归一化后的点：")
for p in normed_points:
    print(p)
print("标准x坐标：", x_centers)
print("标准y坐标：", y_centers)
